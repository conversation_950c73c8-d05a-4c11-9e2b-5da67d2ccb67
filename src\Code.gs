/**
 * SIGMA - Système Informatique de Gestion du Matériel
 * Code principal Google Apps Script
 */

function doGet(e) {
  try {
    const page = e.parameter.page || 'dashboard';
    console.log(`📄 Requête reçue pour la page: ${page}`);

    switch (page) {
      case 'login':
        return serveLoginPage();
      case 'dashboard':
        return serveDashboard(); // Maintenant, cette fonction servira le bon fichier
      case 'emprunts':
        return serveEmprunts();
      // ... autres routes
      default:
        return serveDashboard();
    }
  } catch (error) {
    console.error('❌ Erreur dans doGet:', error);
    return serveErrorPage(error.message);
  }
}

/**
 * Servir la page de connexion
 */
function serveLoginPage() {
  const template = HtmlService.createTemplateFromFile('html/login');
  return template.evaluate()
      .setTitle('SIGMA - Connexion')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');
}

/**
 * CORRECTION: Servir le dashboard principal au lieu de la page de login
 */
function serveDashboard() {
  try {
    const template = HtmlService.createTemplateFromFile('html/dashboard');
    return template.evaluate()
      .setTitle('SIGMA - Dashboard')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');
  } catch (error) {
    console.error('❌ Erreur lors du chargement du dashboard:', error);
    throw error;
  }
}

// ... Le reste de vos fonctions (serveEmprunts, serveErrorPage, etc.) reste inchangé ...

/**
 * Servir la page des emprunts (Exemple, laissé tel quel)
 */
function serveEmprunts() {
    return serveLoginPage();
}

/**
 * Servir une page d'erreur
 */
function serveErrorPage(errorMessage) {
  const html = HtmlService.createHtmlOutput(`
      <!DOCTYPE html><html><head><title>SIGMA - Erreur</title></head>
      <body><h1>⚠️ Erreur</h1><p>${errorMessage}</p></body></html>`);
  return html.setTitle('SIGMA - Erreur').setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}
