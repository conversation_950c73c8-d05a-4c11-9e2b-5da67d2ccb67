import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e_tests',
  fullyParallel: true,
  reporter: 'html',
  use: {
    // La baseURL est l'URL que le webServer va servir.
    baseURL: 'http://127.0.0.1:8080',
    trace: 'on-first-retry',
  },

  // La configuration webServer est la clé. Playwright va:
  // 1. Exécuter cette commande pour démarrer le serveur.
  // 2. Attendre que l'URL soit accessible.
  // 3. Lancer les tests.
  // 4. Arrêter le serveur à la fin.
  webServer: {
    command: 'npx http-server ./src -p 8080 --silent',
    url: 'http://127.0.0.1:8080/html/dashboard.html', // On attend que la page spécifique soit prête
    reuseExistingServer: !process.env.CI,
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
});
