/**
 * Tests d'intégration pour les fonctions dashboard
 * Tests robustes utilisant firebase-functions-test
 */

import * as admin from 'firebase-admin';
import functionsTest from 'firebase-functions-test';

// Mock des utilitaires de monitoring
jest.mock('../../utils/monitoring', () => ({
  sendCriticalStockCount: jest.fn(),
  sendCriticalOverdueCount: jest.fn(),
  sendFirestoreReadRate: jest.fn(),
  withMonitoring: (name: string, fn: Function) => fn
}));

// Initialiser firebase-functions-test en mode offline pour les tests
const testEnv = functionsTest();

// Importer les fonctions après l'initialisation
import { getDashboardData } from '../../dashboard/getDashboardData';
import { getStockAlerts } from '../../dashboard/getStockAlerts';
import { getOverdueEmprunts } from '../../dashboard/getOverdueEmprunts';

describe('Dashboard Functions Integration', () => {
  beforeAll(async () => {
    // Configurer l'environnement de test
    process.env.NODE_ENV = 'test';
  });

  afterAll(async () => {
    // Nettoyer l'environnement de test
    testEnv.cleanup();
  });

  describe('getDashboardData', () => {
    test('devrait retourner une structure de données valide avec tous les tableaux', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      // Wrapper la fonction pour les tests
      const wrappedFunction = testEnv.wrap(getDashboardData);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');

      // Vérifier la structure complète du dashboard
      expect(result).toHaveProperty('stockAlerts');
      expect(result).toHaveProperty('missingMaterial');
      expect(result).toHaveProperty('overdueEmprunts');
      expect(result).toHaveProperty('upcomingEmprunts');
      expect(result).toHaveProperty('nonOpModules');
      expect(result).toHaveProperty('nonOpMaterial');
      expect(result).toHaveProperty('pendingEmprunts');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('performance');

      // Vérifier que tous les tableaux sont des arrays
      expect(Array.isArray(result.stockAlerts)).toBe(true);
      expect(Array.isArray(result.missingMaterial)).toBe(true);
      expect(Array.isArray(result.overdueEmprunts)).toBe(true);
      expect(Array.isArray(result.upcomingEmprunts)).toBe(true);
      expect(Array.isArray(result.nonOpModules)).toBe(true);
      expect(Array.isArray(result.nonOpMaterial)).toBe(true);
      expect(Array.isArray(result.pendingEmprunts)).toBe(true);

      // Vérifier la structure performance
      expect(result.performance).toHaveProperty('totalQueries');
      expect(result.performance).toHaveProperty('executionTimeMs');
      expect(typeof result.performance.totalQueries).toBe('number');
      expect(typeof result.performance.executionTimeMs).toBe('number');

      // Vérifier que le timestamp est valide
      expect(typeof result.timestamp).toBe('string');
      expect(new Date(result.timestamp).getTime()).toBeGreaterThan(0);
    });

    test('devrait fonctionner avec différents rôles en mode test', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'utilisateur' }
        }
      };

      const wrappedFunction = testEnv.wrap(getDashboardData);
      const result = await wrappedFunction(data, context);

      // En mode test, l'autorisation est bypassée, donc ça devrait fonctionner
      expect(result).toBeDefined();
      expect(result).toHaveProperty('stockAlerts');
    });

    test('devrait fonctionner sans authentification en mode test', async () => {
      const data = {};
      const context = {}; // Pas d'auth

      const wrappedFunction = testEnv.wrap(getDashboardData);
      const result = await wrappedFunction(data, context);

      // En mode test, l'autorisation est bypassée, donc ça devrait fonctionner
      expect(result).toBeDefined();
      expect(result).toHaveProperty('stockAlerts');
    });
  });

  describe('getStockAlerts', () => {
    test('devrait retourner des alertes stock avec structure complète', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const wrappedFunction = testEnv.wrap(getStockAlerts);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');

      // Vérifier la structure complète des alertes stock (en mode test)
      expect(result).toHaveProperty('alerts');
      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('timestamp');

      // Vérifier que alerts est un array
      expect(Array.isArray(result.alerts)).toBe(true);

      // Vérifier la structure du summary
      expect(result.summary).toHaveProperty('total');
      expect(result.summary).toHaveProperty('critical');
      expect(result.summary).toHaveProperty('warning');
      expect(result.summary).toHaveProperty('info');
      expect(typeof result.summary.total).toBe('number');
      expect(typeof result.summary.critical).toBe('number');
      expect(typeof result.summary.warning).toBe('number');
      expect(typeof result.summary.info).toBe('number');

      // En mode test, toutes les valeurs devraient être 0
      expect(result.summary.total).toBe(0);
      expect(result.summary.critical).toBe(0);
      expect(result.summary.warning).toBe(0);
      expect(result.summary.info).toBe(0);
    });

    test('devrait filtrer par sévérité critique', async () => {
      const data = { severity: 'critical' };
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const wrappedFunction = testEnv.wrap(getStockAlerts);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
      expect(result).toHaveProperty('alerts');
      expect(Array.isArray(result.alerts)).toBe(true);

      // En mode test, les alertes sont vides mais la structure doit être correcte
      expect(result.summary.total).toBe(0);
      expect(result.summary.critical).toBe(0);
    });
  });

  describe('getOverdueEmprunts', () => {
    test('devrait retourner des emprunts en retard avec structure complète', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const wrappedFunction = testEnv.wrap(getOverdueEmprunts);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');

      // Vérifier la structure complète des emprunts en retard (en mode test)
      expect(result).toHaveProperty('emprunts');
      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('timestamp');

      // Vérifier que emprunts est un array
      expect(Array.isArray(result.emprunts)).toBe(true);

      // Vérifier la structure du summary
      expect(result.summary).toHaveProperty('total');
      expect(result.summary).toHaveProperty('high');
      expect(result.summary).toHaveProperty('medium');
      expect(result.summary).toHaveProperty('low');
      expect(result.summary).toHaveProperty('averageDaysOverdue');
      expect(typeof result.summary.total).toBe('number');
      expect(typeof result.summary.high).toBe('number');
      expect(typeof result.summary.medium).toBe('number');
      expect(typeof result.summary.low).toBe('number');
      expect(typeof result.summary.averageDaysOverdue).toBe('number');

      // En mode test, toutes les valeurs devraient être 0
      expect(result.summary.total).toBe(0);
      expect(result.summary.high).toBe(0);
      expect(result.summary.medium).toBe(0);
      expect(result.summary.low).toBe(0);
      expect(result.summary.averageDaysOverdue).toBe(0);
    });

    test('devrait filtrer par priorité', async () => {
      const data = { priority: 'high' };
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const wrappedFunction = testEnv.wrap(getOverdueEmprunts);
      const result = await wrappedFunction(data, context);

      expect(result).toBeDefined();
      expect(result).toHaveProperty('emprunts');
      expect(Array.isArray(result.emprunts)).toBe(true);

      // En mode test, les emprunts sont vides mais la structure doit être correcte
      expect(result.summary.total).toBe(0);
      expect(result.summary.high).toBe(0);
    });
  });

  describe('Autorisation (mode production simulé)', () => {
    beforeEach(() => {
      // Temporairement désactiver le mode test pour tester l'autorisation
      delete process.env.NODE_ENV;
    });

    afterEach(() => {
      // Remettre le mode test
      process.env.NODE_ENV = 'test';
    });

    test('getDashboardData devrait rejeter les utilisateurs non autorisés', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'utilisateur' }
        }
      };

      const wrappedFunction = testEnv.wrap(getDashboardData);

      await expect(wrappedFunction(data, context))
        .rejects
        .toThrow('Erreur lors de la récupération des données dashboard');
    });

    test('getDashboardData devrait rejeter les utilisateurs non authentifiés', async () => {
      const data = {};
      const context = {}; // Pas d'auth

      const wrappedFunction = testEnv.wrap(getDashboardData);

      await expect(wrappedFunction(data, context))
        .rejects
        .toThrow('Erreur lors de la récupération des données dashboard');
    });

    test('getStockAlerts devrait rejeter les utilisateurs non autorisés', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'utilisateur' }
        }
      };

      const wrappedFunction = testEnv.wrap(getStockAlerts);

      await expect(wrappedFunction(data, context))
        .rejects
        .toThrow('Erreur lors de la récupération des alertes stock');
    });
  });

  describe('Performance', () => {
    test('getDashboardData devrait s\'exécuter rapidement', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const startTime = Date.now();

      const wrappedFunction = testEnv.wrap(getDashboardData);
      const result = await wrappedFunction(data, context);

      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(1000); // 1 seconde max en mode test

      // Vérifier que les métriques de performance sont incluses
      expect(result.performance.executionTimeMs).toBeGreaterThan(0);
      expect(result.performance.totalQueries).toBe(7); // 7 requêtes parallèles
    });

    test('getStockAlerts devrait s\'exécuter rapidement', async () => {
      const data = {};
      const context = {
        auth: {
          uid: 'test-user',
          token: { role: 'regisseur' }
        }
      };

      const startTime = Date.now();

      const wrappedFunction = testEnv.wrap(getStockAlerts);
      const result = await wrappedFunction(data, context);

      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(1000); // 1 seconde max en mode test

      // En mode test, getStockAlerts n'a pas de propriété performance
      // mais devrait avoir la structure de base
      expect(result).toHaveProperty('alerts');
      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('timestamp');
    });
  });

});
