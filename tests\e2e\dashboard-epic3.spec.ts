import { test, expect } from '@playwright/test';

/**
 * Tests E2E pour l'Épique E-3 : Dashboard & Alerting
 * 
 * Ces tests valident les fonctionnalités clés du dashboard SIGMA :
 * - Navigation et structure
 * - Performance de chargement
 * - Alertes et notifications
 * - Filtrage en temps réel
 * - Sécurité et permissions
 * - Métriques de performance
 */

test.describe('Dashboard SIGMA - Épique E-3', () => {
  
  test.beforeEach(async ({ page }) => {
    // Configuration des intercepteurs réseau pour mocker les données
    await page.route('**/getDashboardData', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stockAlerts: [
            { id: '1', nom: 'Article Test', quantite: 2, seuilAlerte: 5, severity: 'critical' }
          ],
          missingMaterial: [],
          overdueEmprunts: [],
          upcomingEmprunts: [],
          nonOpModules: [],
          nonOpMaterial: [],
          pendingEmprunts: [],
          timestamp: new Date().toISOString(),
          performance: { executionTimeMs: 150, totalQueries: 7 }
        })
      });
    });

    await page.route('**/getStockAlerts', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          alerts: [
            { id: '1', nom: 'Article Critique', quantite: 1, seuilAlerte: 5, severity: 'critical' }
          ],
          summary: { total: 1, critical: 1, warning: 0 },
          timestamp: new Date().toISOString()
        })
      });
    });
  });

  test('1. Navigation vers le dashboard', async ({ page }) => {
    // Naviguer vers le dashboard
    await page.goto('/html/dashboard.html');

    // Vérifier que la page se charge correctement
    await expect(page).toHaveTitle(/SIGMA - Dashboard/);
    
    // Vérifier le titre principal
    const mainTitle = page.locator('h1');
    await expect(mainTitle).toBeVisible({ timeout: 10000 });
    await expect(mainTitle).toContainText('Dashboard SIGMA');

    // Vérifier la structure de base
    await expect(page.locator('.dashboard-header')).toBeVisible();
    
    // Vérifier la présence de la grille dashboard (peut échouer si non implémentée)
    const dashboardGrid = page.locator('.dashboard-grid');
    await expect(dashboardGrid).toBeVisible({ timeout: 5000 }).catch(() => {
      console.log('⚠️ Élément .dashboard-grid non trouvé - structure incomplète');
    });
  });

  test('2. Affichage des 7 tableaux du dashboard', async ({ page }) => {
    await page.goto('/html/dashboard.html');

    // Attendre le chargement de la page
    await page.waitForLoadState('networkidle');

    // Vérifier la présence des 7 cartes principales
    const dashboardCards = page.locator('.dashboard-card');
    
    try {
      await expect(dashboardCards).toHaveCount(7, { timeout: 10000 });
      console.log('✅ Les 7 tableaux du dashboard sont présents');
    } catch (error) {
      console.log('❌ Les 7 tableaux du dashboard ne sont pas tous présents');
      const actualCount = await dashboardCards.count();
      console.log(`Nombre de cartes trouvées: ${actualCount}/7`);
      throw error;
    }
  });

  test('3. Chargement des données en moins de 2 secondes', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/html/dashboard.html');
    
    // Attendre que les métriques de performance soient visibles
    const performanceMetrics = page.locator('#performance-metrics');
    
    try {
      await expect(performanceMetrics).toBeVisible({ timeout: 2000 });
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(2000);
      
      console.log(`✅ Chargement en ${loadTime}ms (< 2000ms)`);
    } catch (error) {
      const loadTime = Date.now() - startTime;
      console.log(`❌ Chargement en ${loadTime}ms ou métriques non visibles`);
      throw error;
    }
  });

  test('4. Affichage des alertes stock critiques', async ({ page }) => {
    await page.goto('/html/dashboard.html');

    // Vérifier la section des alertes stock
    const stockAlertsSection = page.locator('#stock-alerts');
    
    try {
      await expect(stockAlertsSection).toBeVisible({ timeout: 10000 });
      
      // Vérifier le contenu des alertes
      const alertContent = page.locator('#stock-alerts .card-content');
      await expect(alertContent).toBeVisible();
      
      console.log('✅ Section alertes stock visible');
    } catch (error) {
      console.log('❌ Section alertes stock non trouvée');
      throw error;
    }
  });

  test('5. Gestion de l\'affichage/masquage des alertes', async ({ page }) => {
    await page.goto('/html/dashboard.html');

    const stockAlertsSection = page.locator('#stock-alerts');
    
    try {
      await expect(stockAlertsSection).toBeVisible({ timeout: 10000 });
      
      // Chercher un bouton de toggle/masquage
      const toggleButton = page.locator('#stock-alerts .toggle-alerts, #stock-alerts .hide-alerts');
      
      if (await toggleButton.count() > 0) {
        await toggleButton.click();
        // Vérifier que l'état change
        await page.waitForTimeout(500);
        console.log('✅ Interaction avec les alertes fonctionnelle');
      } else {
        console.log('⚠️ Bouton de toggle non trouvé');
      }
    } catch (error) {
      console.log('❌ Section alertes non accessible pour interaction');
      throw error;
    }
  });

  test('6. Simulation d\'alerte en temps réel', async ({ page }) => {
    await page.goto('/html/dashboard.html');

    // Simuler une nouvelle alerte via injection de données
    await page.evaluate(() => {
      // Simuler l'arrivée d'une nouvelle alerte
      const event = new CustomEvent('newStockAlert', {
        detail: {
          id: 'test-alert',
          nom: 'Article Test Temps Réel',
          quantite: 0,
          seuilAlerte: 5,
          severity: 'critical'
        }
      });
      window.dispatchEvent(event);
    });

    // Vérifier que l'alerte apparaît
    try {
      const newAlert = page.locator('[data-alert-id="test-alert"]');
      await expect(newAlert).toBeVisible({ timeout: 5000 });
      console.log('✅ Alerte temps réel fonctionnelle');
    } catch (error) {
      console.log('❌ Système d\'alertes temps réel non implémenté');
      throw error;
    }
  });

  test('7. Application des filtres en temps réel', async ({ page }) => {
    await page.goto('/html/dashboard.html');

    // Chercher des éléments de filtrage
    const filterElements = page.locator('.filter-controls, .dashboard-filters, [data-filter]');
    
    try {
      if (await filterElements.count() > 0) {
        const startTime = Date.now();
        
        // Appliquer un filtre
        await filterElements.first().click();
        
        // Vérifier que la réponse est rapide (< 200ms)
        await page.waitForTimeout(200);
        const responseTime = Date.now() - startTime;
        
        expect(responseTime).toBeLessThan(200);
        console.log(`✅ Filtrage en ${responseTime}ms (< 200ms)`);
      } else {
        throw new Error('Aucun élément de filtrage trouvé');
      }
    } catch (error) {
      console.log('❌ Interface de filtrage non implémentée');
      throw error;
    }
  });

  test('8. Validation des permissions d\'accès', async ({ page }) => {
    // Simuler un utilisateur sans permissions
    await page.route('**/getDashboardData', async route => {
      await route.fulfill({
        status: 403,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Accès refusé' })
      });
    });

    await page.goto('/html/dashboard.html');

    try {
      // Vérifier qu'un message d'erreur ou une redirection se produit
      const errorMessage = page.locator('.error-message, .access-denied');
      await expect(errorMessage).toBeVisible({ timeout: 5000 });
      console.log('✅ Contrôle d\'accès fonctionnel');
    } catch (error) {
      console.log('❌ Contrôle d\'accès non implémenté');
      throw error;
    }
  });

  test('9. Affichage des métriques de performance', async ({ page }) => {
    await page.goto('/html/dashboard.html');

    const performanceMetrics = page.locator('#performance-metrics');
    
    try {
      await expect(performanceMetrics).toBeVisible({ timeout: 10000 });
      
      // Vérifier que les métriques contiennent des données
      const metricsText = await performanceMetrics.textContent();
      expect(metricsText).toBeTruthy();
      
      console.log('✅ Métriques de performance visibles');
    } catch (error) {
      console.log('❌ Métriques de performance cachées ou non implémentées');
      throw error;
    }
  });

  test('10. Test de robustesse avec données mockées', async ({ page }) => {
    // Test avec des données volumineuses
    await page.route('**/getDashboardData', async route => {
      const largeDataset = {
        stockAlerts: Array.from({ length: 50 }, (_, i) => ({
          id: `alert-${i}`,
          nom: `Article ${i}`,
          quantite: Math.floor(Math.random() * 10),
          seuilAlerte: 5,
          severity: i % 3 === 0 ? 'critical' : 'warning'
        })),
        missingMaterial: Array.from({ length: 20 }, (_, i) => ({ id: `missing-${i}` })),
        overdueEmprunts: Array.from({ length: 15 }, (_, i) => ({ id: `overdue-${i}` })),
        upcomingEmprunts: Array.from({ length: 30 }, (_, i) => ({ id: `upcoming-${i}` })),
        nonOpModules: Array.from({ length: 10 }, (_, i) => ({ id: `module-${i}` })),
        nonOpMaterial: Array.from({ length: 25 }, (_, i) => ({ id: `material-${i}` })),
        pendingEmprunts: Array.from({ length: 40 }, (_, i) => ({ id: `pending-${i}` })),
        timestamp: new Date().toISOString(),
        performance: { executionTimeMs: 250, totalQueries: 7 }
      };

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(largeDataset)
      });
    });

    await page.goto('/html/dashboard.html');

    try {
      // Vérifier que la page reste responsive avec beaucoup de données
      await page.waitForLoadState('networkidle', { timeout: 10000 });
      
      const title = page.locator('h1');
      await expect(title).toBeVisible();
      
      console.log('✅ Dashboard robuste avec données volumineuses');
    } catch (error) {
      console.log('❌ Dashboard non robuste avec données volumineuses');
      throw error;
    }
  });
});
