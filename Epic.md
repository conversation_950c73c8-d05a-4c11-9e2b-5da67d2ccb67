---

# Plan Directeur des Épiques SIGMA (v2.2 – enrichi IA)

Chaque épique inclut désormais :
- **intents_requis** : MCP nécessaires.
- **artefacts_verifiables** : preuves de complétion (fichiers, tests, métriques).
- **taches_humaines** : tâches à réaliser par un humain.

---

## Épique E-1 : Sécurité & Authentification

* **Objectif global** : Mettre en place une authentification Google et une gestion fine des rôles pour protéger l'accès.
* **Documents de référence clés** :
  * @docs/Permissions.md
  * @docs/Architecture_SIGMA_v1.2.md
* **Dépendances notables** : firebase-admin
* **Critères de succès** :
  * PR avec implémentation Google OAuth.
  * Custom Claims injectés via CF.
  * Règles Firestore/Storage basées sur les rôles.
  * Tests d'accès non autorisé 100 % KO.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction", "firebase_rules_update", "playwright_e2e_testing"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Résultats des tests Playwright d’accès refusé.
  * Fichier `firestore.rules`.
* **taches_humaines** : Aucune.
* **Pilote** : Agent

---

## Épique E-2 : Flux « Emprunts »

* **Objectif global** : Industrialiser le cycle complet des emprunts : création, départ, retour, et génération de PDF.
* **Documents de référence clés** :
  * @docs/Gestion des Emprunts dans SIGMA Firebase.md
  * @docs/Interface_SIGMA_v1.0.md
  * @docs/DataModel.md
* **Dépendances notables** : pdf-lib, playwright
* **Critères de succès** :
  * PR avec 3 Cloud Functions + tests ≥90 %.
  * Génération PDF < 3s.
  * Tests Playwright du formulaire multi-étapes OK.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction", "playwright_e2e_testing", "pdf_generation"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Rapport de tests unitaires.
  * Rapport Playwright.
* **taches_humaines** : Validation finale de la mise en production.
* **Pilote** : Agent

---

## Épique E-3 : Dashboard & Alerting

* **Objectif global** : Offrir un dashboard temps-réel performant avec des alertes automatiques.
* **Documents de référence clés** :
  * @docs/Onglet Résumé de SIGMA Firebase.md
  * @docs/Interface_SIGMA_v1.0.md
* **Dépendances notables** : lighthouse, playwright
* **Critères de succès** :
  * PR avec dashboard.
  * Listeners temps-réel fonctionnels.
  * Firestore <100 lectures/sec.
  * Alertes Cloud Monitoring actives.
  * Score Lighthouse ≥ 90.
  * Tests Playwright navigation & filtres OK.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction", "playwright_e2e_testing", "lighthouse_audit"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Rapport Lighthouse.
  * Rapport Playwright.
* **taches_humaines** : Configuration manuelle du dashboard Cloud Monitoring.
* **Pilote** : Agent

---

## Épique E-4 : Expérience Stocks & Livraisons

* **Objectif global** : Améliorer la visualisation logistique avec filtres avancés et carte interactive.
* **Documents de référence clés** :
  * @docs/État des Stocks dans SIGMA.md
  * @docs/Interface_SIGMA_v1.0.md
  * @docs/Gestion des Modules dans SIGMA.md
* **Dépendances notables** : leaflet
* **Critères de succès** :
  * PR écran Stocks + filtres dynamiques <200ms.
  * Carte Leaflet fonctionnelle.
  * Tests UX validés par régisseurs.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction", "playwright_e2e_testing"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Rapport Playwright.
  * Capture écran carte Leaflet.
* **taches_humaines** :
  * Validation UX par régisseurs.
* **Pilote** : Agent

---

## Épique E-5 : Observabilité & Performance

* **Objectif global** : Instrumenter SIGMA pour détecter régressions de performance et coûts anormaux.
* **Documents de référence clés** :
  * @docs/Architecture_SIGMA_v1.2.md
* **Dépendances notables** : aucune
* **Critères de succès** :
  * PR config-as-code Cloud Monitoring.
  * Alertes latence et quotas actives.
  * Cold-start moyen CF < 400ms.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Rapport Cloud Monitoring exporté.
* **taches_humaines** : Ajustement manuel des alertes si nécessaire.
* **Pilote** : Agent

---

## Épique E-6 : Back-ups & Continuité

* **Objectif global** : Garantir restauration des données et continuité de service.
* **Documents de référence clés** :
  * @docs/Architecture_SIGMA_v1.2.md
* **Dépendances notables** : @google-cloud/firestore
* **Critères de succès** :
  * PR script restauration GCS → Firestore.
  * Test mensuel en staging OK.
  * Smoke-tests 100 % verts après restauration.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction", "shell_execution"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Logs de restauration.
  * Rapport smoke-tests.
* **taches_humaines** :
  * Lancement du test mensuel.
* **Pilote** : Agent

---

## Épique E-7 : Qualité & Sécurité

* **Objectif global** : Renforcer la base de code contre vulnérabilités et garantir conformité.
* **Documents de référence clés** :
  * @docs/Permissions.md
  * @docs/Architecture_SIGMA_v1.2.md
* **Dépendances notables** : eslint-plugin-security
* **Critères de succès** :
  * PR corrections audit sécurité OWASP.
  * Règles Storage revues.
  * Accessibilité WCAG AA validée.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction", "playwright_e2e_testing"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Rapport d’audit sécurité.
  * Rapport Playwright accessibilité.
* **taches_humaines** :
  * Validation accessibilité manuelle.
* **Pilote** : Agent

---

## Épique E-8 : Pré-production & Release

* **Objectif global** : Verrouiller périmètre fonctionnel, tester rollback, préparer v1.0.
* **Documents de référence clés** :
  * @docs/Plan de Développement Détaillé - Application SIGMA.md
* **Dépendances notables** : aucune
* **Critères de succès** :
  * Checklist pré-production signée.
  * PR rollback automatisé + testé.
  * Tag v1.0.0 créé sur main.
* **intents_requis** : ["serena_code_modification", "shell_execution"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Script rollback.
* **taches_humaines** :
  * Signature checklist pré-prod.
* **Pilote** : Agent & Humain

---

## Épique E-9 : Gouvernance des Données

* **Objectif global** : Stabiliser modèle de données et documentation technique.
* **Documents de référence clés** :
  * @docs/DataModel.md
* **Dépendances notables** : zod
* **Critères de succès** :
  * PR génération automatique schémas Zod.
  * DataModel.md gelé en v0.9 validé.
  * Documentation ≥ 95 %.
* **intents_requis** : ["serena_code_modification", "firebase_backend_interaction"]
* **artefacts_verifiables** :
  * PR GitHub.
  * Rapport couverture documentation.
* **taches_humaines** :
  * Validation finale documentation.
* **Pilote** : Agent

---

## Épique E-10 : Vision Produit & Feedback

* **Objectif global** : Aligner roadmap sur retours utilisateurs pour futures versions.
* **Documents de référence clés** : N/A
* **Dépendances notables** : N/A
* **Critères de succès** :
  * Atelier story-mapping terminé.
  * Retours utilisateurs consolidés.
  * Roadmap v1.1 publiée et validée.
* **intents_requis** : []
* **artefacts_verifiables** :
  * Document roadmap.
  * Compte-rendu atelier.
* **taches_humaines** :
  * Conduite de l’atelier.
  * Consolidation des retours.
* **Pilote** : Humain

---
