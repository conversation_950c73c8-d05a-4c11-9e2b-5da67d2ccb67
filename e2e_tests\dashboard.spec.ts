import { test, expect } from '@playwright/test';

test.describe('Dashboard SIGMA', () => {
  test('devrait ouvrir le dashboard et trouver le titre principal', async ({ page }) => {
    // On navigue vers la page relative à la baseURL
    await page.goto('/html/dashboard.html');

    const mainTitle = page.locator('h1');
    await expect(mainTitle).toBeVisible({ timeout: 10000 });
    await expect(mainTitle).toContainText('Dashboard SIGMA');
  });
});
