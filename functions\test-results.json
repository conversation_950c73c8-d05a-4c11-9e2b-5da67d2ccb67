{"config": {"configFile": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 2, "webServer": null}, "suites": [], "errors": [{"message": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\index.js:61:33)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .js] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .js] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .js] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\playwright.config.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module.newCompile2 (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:46:29)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .ts] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at requireOrImport (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\transform\\transform.js:218:18)\n    at loadUserConfig (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\common\\configLoader.js:106:89)\n    at loadConfig (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\common\\configLoader.js:118:28)\n    at loadConfigFromFile (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\common\\configLoader.js:330:10)\n    at runTests (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\program.js:166:18)\n    at i.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\program.js:65:7)\n\nSecond: ", "stack": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\index.js:61:33)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .js] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .js] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .js] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\playwright.config.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module.newCompile2 (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:46:29)\n    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Object.newLoader2 [as .ts] (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\third_party\\pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at requireOrImport (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\transform\\transform.js:218:18)\n    at loadUserConfig (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\common\\configLoader.js:106:89)\n    at loadConfig (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\common\\configLoader.js:118:28)\n    at loadConfigFromFile (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\common\\configLoader.js:330:10)\n    at runTests (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\program.js:166:18)\n    at i.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\playwright\\lib\\program.js:65:7)\n\nSecond: \n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\index.js:61:33)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\test.js:17:13)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\playwright.config.ts:1:1)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\node_modules\\@playwright\\test\\index.js:17:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:1:1)", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\playwright.config.ts", "column": 1, "line": 1}, "snippet": "\u001b[90m   at \u001b[39m..\\..\\functions\\playwright.config.ts:1\n\n\u001b[0m\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 1 |\u001b[39m \u001b[36mimport\u001b[39m { defineConfig\u001b[33m,\u001b[39m devices } \u001b[36mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m \u001b[90m/**\u001b[39m\n \u001b[90m 4 |\u001b[39m \u001b[90m * Configuration Playwright pour les tests E2E SIGMA\u001b[39m\u001b[0m"}, {"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-08-08T18:30:29.096Z", "duration": 827.3510000000006, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}