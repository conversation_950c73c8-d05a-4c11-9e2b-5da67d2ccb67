{"name": "functions", "version": "1.0.0", "main": "lib/index.js", "scripts": {"build": "tsc -p tsconfig.json", "serve": "npm run build && firebase emulators:start --only functions,firestore,auth", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "format": "prettier --write \"src/**/*.ts\"", "lint": "eslint src/**/*.ts", "lint:fix": "eslint \"src/**/*.ts\" --fix", "test": "jest --config jest.config.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "npm run build && firebase emulators:exec --only auth,firestore,functions --project sigma-nova \"jest --config jest.config.js --runInBand\"", "test:e2e": "cd .. && npx playwright test tests/e2e/dashboard-epic3.spec.ts --project=chromium", "test:e2e:all": "cd .. && npx playwright test", "test:e2e:report": "cd .. && npx playwright show-report", "serve:html": "http-server ../src/html -p 8080 --silent", "audit:lighthouse:run": "lighthouse http://127.0.0.1:8080/dashboard.html --output json --output-path ../lighthouse-report.json --view --only-categories=performance", "audit:lighthouse:wait-and-run": "wait-on http://127.0.0.1:8080/dashboard.html && npm run audit:lighthouse:run", "audit:lighthouse": "concurrently -k -s first \"npm:serve:html\" \"npm:audit:lighthouse:wait-and-run\""}, "engines": {"node": "18"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-cloud/monitoring": "^5.3.0", "firebase-functions": "^6.4.0", "pdf-lib": "^1.17.1", "zod": "^4.0.14"}, "devDependencies": {"@firebase/rules-unit-testing": "^3.0.4", "@playwright/test": "^1.54.2", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "concurrently": "^8.2.2", "eslint": "^8.57.1", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^5.5.4", "firebase-admin": "^13.4.0", "firebase-functions-test": "^3.4.1", "firebase-tools": "^13.0.0", "http-server": "^14.1.1", "jest": "^29.7.0", "lighthouse": "^11.7.0", "prettier": "^3.6.2", "ts-jest": "^29.4.1", "typescript": "^5.9.2", "wait-on": "^7.2.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/src/tests/setup.ts"]}}