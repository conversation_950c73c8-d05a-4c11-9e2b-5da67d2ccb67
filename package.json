{"name": "sigma-agent", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test:e2e": "playwright test"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.54.2", "eslint": "^8.57.1"}, "directories": {"doc": "docs", "test": "tests"}, "dependencies": {"acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "ajv": "^6.12.6", "ansi-regex": "^5.0.1", "ansi-styles": "^4.3.0", "argparse": "^2.0.1", "balanced-match": "^1.0.2", "brace-expansion": "^1.1.12", "callsites": "^3.1.0", "chalk": "^4.1.2", "color-convert": "^2.0.1", "color-name": "^1.1.4", "concat-map": "^0.0.1", "cross-spawn": "^7.0.6", "debug": "^4.4.1", "deep-is": "^0.1.4", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.6.0", "esrecurse": "^4.3.0", "estraverse": "^5.3.0", "esutils": "^2.0.3", "fast-deep-equal": "^3.1.3", "fast-json-stable-stringify": "^2.1.0", "fast-levenshtein": "^2.0.6", "fastq": "^1.19.1", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "flat-cache": "^3.2.0", "flatted": "^3.3.3", "fs.realpath": "^1.0.0", "glob": "^7.2.3", "glob-parent": "^6.0.2", "globals": "^13.24.0", "graphemer": "^1.4.0", "has-flag": "^4.0.0", "ignore": "^5.3.2", "import-fresh": "^3.3.1", "imurmurhash": "^0.1.4", "inflight": "^1.0.6", "inherits": "^2.0.4", "is-extglob": "^2.1.1", "is-glob": "^4.0.3", "is-path-inside": "^3.0.3", "isexe": "^2.0.0", "js-yaml": "^4.1.0", "json-buffer": "^3.0.1", "json-schema-traverse": "^0.4.1", "json-stable-stringify-without-jsonify": "^1.0.1", "keyv": "^4.5.4", "levn": "^0.4.1", "locate-path": "^6.0.0", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "ms": "^2.1.3", "natural-compare": "^1.4.0", "once": "^1.4.0", "optionator": "^0.9.4", "p-limit": "^3.1.0", "p-locate": "^5.0.0", "parent-module": "^1.0.1", "path-exists": "^4.0.0", "path-is-absolute": "^1.0.1", "path-key": "^3.1.1", "playwright": "^1.54.2", "playwright-core": "^1.54.2", "prelude-ls": "^1.2.1", "punycode": "^2.3.1", "queue-microtask": "^1.2.3", "resolve-from": "^4.0.0", "reusify": "^1.1.0", "rimraf": "^3.0.2", "run-parallel": "^1.2.0", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.1", "supports-color": "^7.2.0", "text-table": "^0.2.0", "type-check": "^0.4.0", "type-fest": "^0.20.2", "uri-js": "^4.4.1", "which": "^2.0.2", "word-wrap": "^1.2.5", "wrappy": "^1.0.2", "yocto-queue": "^0.1.0"}, "description": ""}