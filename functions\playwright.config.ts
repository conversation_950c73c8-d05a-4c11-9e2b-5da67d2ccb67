import { defineConfig, devices } from '@playwright/test';

/**
 * Configuration Playwright pour les tests E2E SIGMA
 * Tests spécifiques pour l'Épique E-3 : Dashboard & Alerting
 */
export default defineConfig({
  // Répertoire des tests E2E
  testDir: '../tests/e2e',
  
  // Timeout global pour les tests
  timeout: 30000,
  
  // Timeout pour les attentes (expect)
  expect: {
    timeout: 5000,
  },
  
  // Configuration pour l'exécution
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // Configuration des rapports
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'test-results.json' }],
    ['junit', { outputFile: 'test-results.xml' }],
    ['list']
  ],
  
  // Configuration globale
  use: {
    // URL de base pour les tests
    baseURL: 'http://127.0.0.1:8080',
    
    // Capture d'écran en cas d'échec
    screenshot: 'only-on-failure',
    
    // Enregistrement vidéo en cas d'échec
    video: 'retain-on-failure',
    
    // Trace en cas d'échec
    trace: 'on-first-retry',
    
    // Timeout pour les actions
    actionTimeout: 10000,
    
    // Timeout pour la navigation
    navigationTimeout: 15000,
  },

  // Configuration des projets (navigateurs)
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    
    // Tests mobiles
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  // Configuration du serveur web pour les tests (désactivé pour l'instant)
  // webServer: {
  //   command: 'npm run serve:html',
  //   port: 8080,
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120000,
  // },
});
